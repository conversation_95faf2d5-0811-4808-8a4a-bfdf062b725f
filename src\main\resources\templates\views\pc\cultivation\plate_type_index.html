<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>板块类型管理</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateDailog.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_+_VR_}+'/css/cultivation/plateType.css'(v=${new java.util.Date().getTime()})}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/layui.js'}"></script>
    <style>
        .tab-box1 .layui-table-view .layui-table td[data-field="plateTypeLevelName"] .layui-table-cell {
            padding: 0 !important;
        }
        #teachPlanCourseSet .tab-box .layui-inline {
            margin-bottom: 16px;
        }
        #teachPlanCourseSet .tab-box .j-search-con {
            width: 200px;
        }
    </style>
</head>

<body>
<div class="main">
    <ul class="tab-nav">
        <li class="active">板块类型管理</li>
        <li>板块类型课程维护</li>
    </ul>
    <div class="tab-box">
        <div class="tab-box-con tab-box1">
            <div class="main">
                <div class="table-opt">
                    <button type="submit" class="layui-btn" id="addPlateLevel">添加</button>
                </div>
                <div class="table-box">
                    <table class="layui-hide" id="plateType" lay-filter="plateType"></table>
                </div>
            </div>
        </div>
        <div class="tab-box-con tab-box2">
            <div class="main" style="padding:24px 32px;">
                <form action="" class="layui-form form-search" lay-filter='formSearch'>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">学年学期</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" readonly="" class="schoolSel" name="semester"
                                       formAlias="plateTypeCourse" fieldAlias="semester" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">板块类型名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" readonly="" class="schoolSel"
                                       name="plateTypeName"
                                       formAlias="plateType" fieldAlias="name" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">板块类型级别</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" readonly="" class="schoolSel"
                                       name="plateTypeLevelName"
                                       formAlias="plateTypeLevel" fieldAlias="levelName" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">课程/课程组名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" readonly="" class="schoolSel" name="courseName"
                                       formAlias="plateTypeCourse" fieldAlias="courseName" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="submit" class="layui-btn " lay-submit lay-filter="formSearch">查询</button>
                    </div>
                </form>
                <div class="table-opt" style="margin: 0 0 16px 0;">
                    <button type="submit" class="layui-btn" id="addPlateType">添加</button>
                    <button type="submit" class="layui-btn" id="setTeachPlanCourse">设置对应课程</button>
                </div>
                <div class="table-box" style="margin: 0;">
                    <table class="layui-hide" id="plateTypeCourse" lay-filter="plateTypeCourse"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 添加弹窗 -->
<div id="addType" class="dialog">
    <div class="dialog-title">
        <h5>添加</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <div class="item-title">板块类型基本信息设置</div>
        <form action="" class="layui-form form-message" lay-filter='formMessage'>
            <input type="hidden" name="id" disabled>
            <div class="layui-form-item">
                <label class="layui-form-label">板块类型代码</label>
                <div class="layui-input-block">
                    <input type="text" value="FX001" name="code" disabled autocomplete="off"
                           class="layui-input layui-disabled">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>板块类型名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" required lay-verify="required"
                           placeholder="请输入内容。如：体育选修"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
        </form>
        <div class="item-title">板块类型级别设置</div>
        <div class="table-box">
            <button class="layui-btn" id="addLevelBtn">添加</button>
            <table class="layui-hide" id="plateTypeLevel" lay-filter="plateTypeLevel"></table>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnPlateCancel">取消</button>
        <button id="btnPlateSure">确定</button>
    </div>
</div>
<!-- 添加层级 -->
<div id="addLevel" class="dialog">
    <div class="dialog-title">
        <h5>添加</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-level" lay-filter='formLevel'>
            <input type="hidden" name="id" disabled>
            <div class="layui-form-item">
                <label class="layui-form-label">分项类型代码</label>
                <div class="layui-input-block">
                    <input type="text" value="FX001" name="typeCode" disabled autocomplete="off"
                           class="layui-input layui-disabled">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>对应级别</label>
                <div class="layui-input-block">
                    <input type="text" name="levelName" required lay-verify="required" placeholder="请输入"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>是否启用</label>
                <div class="layui-input-block">
                    <div class="switch">
                        <div class="switch-con active" id="switchUse"><i></i></div>
                        <div class="switch-text">开启</div>
                    </div>

                </div>
            </div>
        </form>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnLevelCancel">取消</button>
        <button id="btnLevelSure">确定</button>
    </div>
</div>
<!-- 添加 -->
<div class="dialog" id="addChildType">
    <div class="dialog-title">
        <h5>添加</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-message" lay-filter='formCourseMessage'>
            <input type="hidden" name="id" disabled>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>学年学期</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="text" name="semester" placeholder="请选择" readonly="" class="schoolSel"
                               formAlias="xnxq" fieldAlias="xnxq_xnxqh">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>板块类型名称</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="hidden" name="plateTypeId"/>
                        <input type="text" name="plateTypeName" placeholder="请选择" readonly="" class="schoolSel"
                               formAlias="plateType" source="table" fieldAlias="name">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>
                    对应级别
                    <span class="tips"><em>先选择学年学期，再选择对应级别</em></span>
                </label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="hidden" name="plateTypeLevelId"/>
                        <input type="text" name="plateTypeLevelName" placeholder="请选择" readonly="" class="schoolSel"
                               formAlias="plateTypeLevel" source="relation" fieldAlias="levelName">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>开课校区</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="text" name="campusArea" placeholder="请选择" readonly="" class="schoolSel"
                               formAlias="campusArea" fieldAlias="campusArea">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnPlateCourseCancel">取消</button>
        <button id="btnPlateCourseSure">确定</button>
    </div>
</div>
<!-- 课程设置 -->
<div class="dialog" id="courseSet">
    <div class="dialog-title">
        <h5>课程设置</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <ul class="tab-nav">
            <li class="active">选择课程</li>
            <li>选择课程组</li>
        </ul>
        <div class="tab-box">
            <div class="tab-box-con">
                <form action="" class="layui-form course-sel" lay-filter="course-sel">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">课程名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="kck_kcmc" placeholder="请选择" readonly=""
                                       class="schoolSel" formAlias="kck" fieldAlias="kck_kcmc">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">课程性质</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="kck_kcxz" placeholder="请选择" readonly=""
                                       class="schoolSel" formAlias="kck" fieldAlias="kck_kcxz">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">选/必修</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="kck_kcsx" placeholder="请选择" readonly=""
                                       class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                        <li>选修</li>
                                        <li>必修</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">开课系部</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="kck_kkyx" placeholder="请选择" readonly="" class="schoolSel"
                                       formAlias="kck" fieldAlias="kck_kkyx">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="submit" class="layui-btn " lay-submit id="selCourseSearch">查询</button>
                    </div>
                </form>
                <table class="layui-hide" id="selCourse" lay-filter="selCourse"></table>
            </div>
            <div class="tab-box-con">
                <form action="" class="layui-form course-group" lay-filter="course-group">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">课程组名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="kcbz_kcbzmc" placeholder="请选择" readonly=""
                                       class="schoolSel" formAlias="kczgl" fieldAlias="kcbz_kcbzmc">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="submit" class="layui-btn " lay-submit id="courseGroupSearch">查询</button>
                    </div>
                </form>
                <table class="layui-hide" id="selCourseGroup" lay-filter="selCourseGroup"></table>
            </div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnSetCancel">取消</button>
        <button id="btnSetSure">确定</button>
    </div>
</div>
<div class="dialog" id="dialogTip" style="display: none;">
    <div class="dialog-con">
        <img src="/images/cultivation/mooc/tips-error.png" alt="">
        <p>保存成功</p>
        <button id="tipSuccessBtn">确定</button>
    </div>
</div>
<div class="dialog" id="teachPlanCourseSet" style="display: none">
    <div class="dialog-title">
        <h5>教学计划课程设置</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <div class="tab-box" style="margin-top: 24px;">
            <form action="" class="layui-form teach-plan-course-sel" lay-filter="teach-plan-course-sel">
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 56px;">课程名称</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="kck_kcmc" placeholder="请选择" readonly=""
                                   class="schoolSel" formAlias="kck" fieldAlias="kck_kcmc">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 56px;">课程性质</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_kcxz" placeholder="请选择" readonly=""
                                   class="schoolSel" formAlias="kck" fieldAlias="kck_kcxz">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 56px;">是否纯实践环节</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_sfcsjhj" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul>
                                    <li>是</li>
                                    <li>否</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 56px;">开课系部</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_kkyx" placeholder="请选择" readonly="" class="schoolSel"
                                   formAlias="kck" fieldAlias="kck_kkyx">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <button type="submit" class="layui-btn " lay-submit id="selTeachPlanCourseSearch">查询</button>
                </div>
            </form>
            <table class="layui-hide" id="selTeachPlanCourse" lay-filter="selTeachPlanCourse"></table>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnTeachPlanSetCancel">取消</button>
        <button id="btnTeachPlanSetSure">确定</button>
    </div>
</div>
</body>
<script type="text/html" id="courseSetToolBar">
    <span class="opt-btn" lay-event="edit">编辑</span>
    <span class="opt-btn color-red" lay-event="del">删除</span>
</script>
<script type="text/html" id="plateTypeToolBar">
    <span class="opt-btn" lay-event="edit">编辑</span>
    {{# if(d.status==1) { }}
    <span class="opt-btn opt-stop" lay-event="stop">停用</span>
    {{#  }else { }}
    <span class="opt-btn opt-stop1 color-yellow" lay-event="stop">启用</span>
    {{# } }}
    <span class="opt-btn color-red" lay-event="del">删除</span>
</script>
<!-- 板块类型等级 -->
<script type="text/html" id="plateLevel">
    <ul class="plate-level">
        {{# layui.each(d.plateTypeLevel, function(i, v){ }}
        <li value="{{= v.id }}">{{= v.levelName }}</li>
        {{# }); }}
    </ul>
</script>
<script type="text/html" id="plateTypeCourseToolBar">
    <span class="opt-btn" lay-event="set">设置课程/课程组</span>
    <span class="opt-btn" lay-event="clear">清空课程/课程组</span>
    <span class="opt-btn color-red" lay-event="del">删除</span>
</script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${fid}]];
    const id = "";
</script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/plateType.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/plateCommon.js'}"></script>
</html>