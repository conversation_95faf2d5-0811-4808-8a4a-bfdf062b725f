
layui.use(['table', 'jquery', 'laypage', 'form', 'layer'], function () {
    const table = layui.table,
        $ = layui.jquery,
        form = layui.form,
        layer = layui.layer;
    // table渲染
    table.render({
        elem: '#plateType',
        url: _VR_ + '/pc/cultivation/plateClass/getPlateClassData',
        where: {fid: fid},
        page: true,
        text: {
            none: '<img src="/images/cultivation/no-data.png" alt="没有数据" style="width: 72px; height: auto; display: block;margin:0 auto"/>'
        },
        parseData: function (data) {
            if (data.status) {
                return {
                    "code": 0, //解析接口状态
                    "msg": data.msg, //解析提示文本
                    "count": data.data.count, //解析数据长度
                    "data": data.data.list //解析数据列表
                }
            } else {
                return {
                    "code": 1, //解析接口状态
                    "msg": "数据获取失败", //解析提示文本
                    "count": 0, //解析数据长度
                    "data": [] //解析数据列表
                }
            }
        },
        height: 'full-220',
        cols: [
            [
                {
                    type: 'checkbox',
                    width: 60,
                    fixed: 'left'
                },
                {
                    field: 'semester',
                    title: '学年学期',
                    width: 160
                },
                {
                    field: 'plateClassName',
                    title: '板块开课名称',
                    width: 160
                },
                {
                    field: 'plateTypeName',
                    title: '板块类型名称'
                },
                {
                    field: 'classNameArr',
                    title: '项目班级',
                    width: 220
                },
                {
                    field: 'number',
                    title: '人数'
                },
                {
                    field: 'capacity',
                    title: '容量'
                },
                {
                    field: 'classTime',
                    title: '上课时间',
                    width: 320,
                    templet: function (d) {
                        if (d.classTime) {
                            const classTime = [];
                            const classTimeArr = JSON.parse(d.classTime);
                            for (let i = 0; i < classTimeArr.length; i++) {
                                classTime.push(classTimeArr[i].ctStr);
                            }
                            return classTime.join(';');
                        }
                        return "";
                    }
                },
                {
                    field: 'opt',
                    title: '操作',
                    fixed: 'right',
                    toolbar: '#plateTypeToolBar',
                    width: 280
                }
            ]
        ],
        done: function (res, curr, count) {
            let table_data = res.data
        }
    })
    // 操作
    let layerTimeIndex;
    table.on('tool(plateType)', function (obj) {
        if (obj.event === 'del') {
            $.post(_VR_ + '/pc/cultivation/plateStartClass/getPlateStartClassData', {
                id: obj.data.id,
                fid: fid
            }, function (result) {
                if (result.data && result.data.length > 0) {
                    $("#dialogTip p").text("已开课无法删除");
                    dialogTipPop();
                    return false;
                }
                layer.confirm('请确认是否删除？', function (index) {
                    $.post(_VR_ + '/pc/cultivation/plateClass/delPlateClass', {
                        id: obj.data.id
                    }, function (result) {
                    });
                    obj.del() // 删除对应行（tr）的DOM结构
                    layer.close(index)
                })
            })
        } else if (obj.event === 'maintainItem') {
            // 维护板块对象
            window.open(_VR_ + '/pc/cultivation/plateClass/maintain?id=' + obj.data.id)
        } else if (obj.event === 'setClassTime') {
            if (obj.data.status !== "未安排") {
                $("#dialogTip p").text("已开课，无法修改上课时间");
                dialogTipPop();
                return false;
            }
            setClassTime(obj.data.semester, obj.data.id);
        }
    })


    //操作每行数据复选框
    let selPlateTypeAry = [];
    getSelData('plateType', selPlateTypeAry)
    /******************************* 添加 start ********************************************* */
    let layerPlateIndex;
    //  添加
    $('#addPlate').click(function () {
        layerPlateIndex = layer.open({
            type: 1,
            title: false,
            closeBtn: false,
            shadeClose: false,
            isOutAnim: true,
            content: $('#addChildType'),
            area: ['auto', 'auto'],
            success: function () {
                // 主动加载学年学期数据，然后设置默认选中
                const semesterInput = $('#addChildType input[name="semester"]');
                const semesterValue = semesterInput.val();
                if (semesterValue) {
                    const formAlias = semesterInput.attr("formAlias");
                    const fieldAlias = semesterInput.attr("fieldAlias");
                    const semesterContainer = semesterInput.parent().find('.j-select-year ul');

                    // 主动加载学年学期数据
                    if (formAlias && fieldAlias && semesterContainer.find('li').length === 0) {
                        $.post(_VR_ + '/teacherIdle/getFormDistinctFiled', {
                            formAlias: formAlias,
                            fieldAlias: fieldAlias
                        }, function (res) {
                            if (res.list) {
                                let selectHtml = "";
                                for (let i = 0; i < res.list.length; i++) {
                                    const isSelected = res.list[i] === semesterValue ? ' class="active"' : '';
                                    selectHtml += "<li" + isSelected + "><span>" + res.list[i] + "</span></li>";
                                }
                                semesterContainer.html(selectHtml);
                            }
                        });
                    } else {
                        // 如果数据已经存在，直接设置选中状态
                        setTimeout(function() {
                            semesterContainer.find('li').each(function() {
                                if ($(this).text() === semesterValue) {
                                    $(this).addClass('active').siblings().removeClass('active');
                                    return false;
                                }
                            });
                        }, 100);
                    }
                }
            }
        })
    })
    //  确定
    $('#btnPlateSure').click(function () {
        // 添加数据
        const formParams = {};
        let selStatus = false;
        // 处理下拉选择框
        $('#addChildType .form-item .schoolSel').each(function () {
            const name = $(this).attr('name');
            const value = $(this).val();
            formParams[name] = value
            if (value === '') selStatus = true
        })
        // 处理普通输入框
        $('#addChildType .form-item input[type="text"]:not(.schoolSel)').each(function () {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (name) {
                formParams[name] = value
                if ($(this).hasClass('layui-input') && $(this).attr('required') && value === '') {
                    selStatus = true
                }
            }
        })
        if (selStatus) {
            layer.msg('您还有项目未选择，请选择后再提交')
            return
        }
        const params = form.val('formItem');
        params.fid = fid;
        params.status = "未安排";
        $.post(_VR_ + '/pc/cultivation/plateClass/addOrUpdatePlateClass', params, function (result) {
            if (!result.status) {
                layer.msg(result.msg)
                return false
            }
            table.reload('plateType')
            layer.close(layerPlateIndex)
        });
    })
    //  取消
    $('#btnPlateCancel,#btnPlanCorrespondCancel,#addChildType .close,#addPlanCorrespond .close').click(function () {
        layer.close(layerPlateIndex)
    })
    /******************************* 添加 end ********************************************* */
    /******************************* 计划对应 start ********************************************* */
    $('#addPlan').click(function () {
        if (selPlateTypeAry.length === 0) {
            layer.msg('请选择数据')
            return false;
        }
        if (selPlateTypeAry.length > 1) {
            layer.msg('只能设置单条')
            return false;
        }
        $("#addPlanCorrespond input[name='semester']").val(selPlateTypeAry[0].semester);
        $("#addPlanCorrespond input[name='plateTypeName']").val(selPlateTypeAry[0].plateTypeName);
        $("#addPlanCorrespond input[name='plateTypeLevelName']").val(selPlateTypeAry[0].plateTypeLevelName);
        if (selPlateTypeAry[0].courseType === 2) {
            $.post(_VR_ + '/pc/cultivation/plateClass/getCourseGroupCourseData', {
                fid: fid,
                courseId: selPlateTypeAry[0].courseId
            }, function (result) {
                if (result.data) {
                    let courseIdArr = [], courseNameArr = [];
                    for (let i = 0; i < result.data.length; i++) {
                        courseIdArr.push(result.data[i].kcbz_kcbh);
                        courseNameArr.push(result.data[i].kcbz_kcmc);
                    }
                    selPlateTypeAry[0].courseId = courseIdArr.join(",");
                    selPlateTypeAry[0].courseName = courseNameArr.join(",");
                }
            });
        }
        // 选中表格数据
        layerPlateIndex = layer.open({
            type: 1,
            title: false,
            closeBtn: false,
            shadeClose: false,
            isOutAnim: true,
            content: $('#addPlanCorrespond'),
            area: ['auto', 'auto'],
            success: function () {
            }
        })
    })

    $("#btnPlanCorrespondSure").click(function () {
        const params = form.val('correspondFormItem');
        let course = $("#addPlanCorrespond .layui-form-item:eq(3) li");
        params.id = selPlateTypeAry[0].id;
        params.courseId = course.attr("id");
        params.courseType = course.attr("courseType");
        params.fid = fid;
        $.post(_VR_ + '/pc/cultivation/plateClass/planCorrespond', params, function (result) {
            if (!result.status) {
                layer.msg(result.msg)
                return false;
            }
            table.reload('plateType')
            layer.close(layerPlateIndex)
        });
    })

    form.on('submit(formSearch)', function (data) {
        data.field.fid = fid;
        table.reload('plateType', {where: data.field, page: {curr: 1}});
        return false;
    })

    $(".form-search button[type='reset']").click(function (data) {
        let field = {
            semester: "",
            plateTypeName: "",
            plateTypeLevelName: ""
        };
        table.reload('plateType', {where: field, page: {curr: 1}});
    })

    /******************************* 计划对应 end ********************************************* */
    /******************************* 安排上课时间 start ********************************************* */
    let plateTimeData = [];
    let lesson;
    let detailId;
    let rowData = {
        id: 1,
        startFestivals: '',
        endFestivals: '',
        weeks: [],
        weekType: ''
    };
    let weeks = 10;
    let cols = [];

    function setClassTime(semester, id) {
        plateTimeData = [];
        cols = [];
        // 安排上课时间
        layerTimeIndex = layer.open({
            type: 1,
            title: false,
            closeBtn: false,
            shadeClose: false,
            isOutAnim: true,
            content: $('#addClassTimeDialog'),
            area: ['auto', 'auto'],
            success: function () {
                detailId = id;
                $.ajax({
                    url: _VR_ + '/pc/cultivation/plateClass/getClassTimeRelevantData',
                    data: {
                        id: id,
                        semester: semester,
                        fid: fid
                    },
                    async: false,
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        lesson = result.data.lesson;
                        if (result.data.semester) {
                            const semester = result.data.semester;
                            weeks = semester.xnxq_jsz - semester.xnxq_qsz;
                            for (let i = 1; i <= weeks; i++) {
                                rowData['week' + i] = i
                                cols.push({
                                    field: 'week' + i,
                                    title: i,
                                    width: 50,
                                    align: 'center'
                                })
                            }
                        }
                        if (result.data.info && result.data.info.classTime) {
                            const classTime = JSON.parse(result.data.info.classTime);
                            for (let i = 0; i < classTime.length; i++) {
                                const item = classTime[i];
                                const newWeeks = {...rowData};
                                newWeeks.startFestivals = item.curriNum.split("-")[0];
                                newWeeks.endFestivals = item.curriNum.split("-")[1];
                                newWeeks.weeks = item.weekNum.split(",");
                                newWeeks.week = numbersStringToDaysOfWeek(item.weekDay);
                                plateTimeData.push(newWeeks);
                            }
                        } else {
                            plateTimeData.push(JSON.parse(JSON.stringify(rowData)))
                        }
                    }
                })
                plateTimeRender()
            }
        })
    }

    function plateTimeRender() {
        // 周次数据
        cols = [
            {
                field: "studentNo",
                title: "星期",
                width: 190,
                templet: "#selectWeek",
            },
            {
                field: "name",
                title: '节次<i class="festivals-tips"></i>',
                width: 300,
                templet: "#selectFestivals",
            },
        ];
        for (i = 1; i <= weeks; i++) {
            rowData["week" + i] = i;
            cols.push({
                field: "week" + i,
                title: i,
                width: 50,
                align: "center",
            });
        }

        cols.push({
            field: "name",
            title: "操作周次",
            fixed: "right",
            toolbar: "#toolBarOpt",
            width: 300,
        });

        table.render({
            elem: '#plateTime',
            data: plateTimeData,
            page: false,
            height: 400,
            text: {
                none: '<img src="/images/cultivation/no-data.png" alt="没有数据" style="width: 72px; height: auto; display: block;margin:0 auto"/>'
            },
            cols: [cols],
            done: function (res, curr, count) {
                if (lesson) {
                    const festivals = lesson.allCurricNums;
                    let html = "";
                    for (let i = 1; i <= festivals; i++) {
                        html += "<li>" + i + "</li>";
                    }
                    $(".festivals .j-select-year ul").html(html);
                }
                $("div[lay-id=plateTime] .schoolSel").off('click').on('click', function () {
                    let parent = $(this).parent()
                    const isHasSlide = parent.hasClass('slideShow');
                    $('.j-search-con').removeClass('slideShow')
                    $('.j-arrow').removeClass('j-arrow-slide')
                    if (isHasSlide) {
                        parent.removeClass('slideShow')
                    } else {
                        parent.addClass('slideShow')
                    }
                    const offsetObj = $(this).offset();
                    const dialogOffset = $("#addClassTimeDialog").offset();
                    const left = offsetObj.left - dialogOffset.left + 'px';
                    const top = offsetObj.top - dialogOffset.top + $(this).outerHeight(true) + 4 + 'px';
                    $(this).parent().find('.j-select-year').css({
                        position: 'fixed',
                        width: '180px',
                        top: top,
                        left: left
                    })
                    return false

                })
                $('div[lay-id=plateTime] .layui-table-main').on('scroll', function () {
                    var ele = $('div[lay-id=plateTime] .layui-table-main .j-search-con.slideShow')
                    if (ele.find('.schoolSel').length) {
                        var offsetObj = ele.find('.schoolSel').offset()
                        var dialogOffset = $('#AddClassTime').offset()
                        ele.find('.j-select-year').css({
                            left: offsetObj.left - dialogOffset.left + 'px'
                        })
                    }
                })

                // 节次提示
                $(".festivals-tips").on('mouseenter', function () {
                    const tipsBox = $("#tipsBox");
                    tipsBox.text('需要在排课模块维护对应学期课表结构')
                    tipsBox.show()
                    const offsetObj = $(this).offset();
                    const left = offsetObj.left - (tipsBox.width() / 2) - 6;
                    const top = offsetObj.top - 40;
                    tipsBox.css({left: left, top: top})
                }).on('mouseleave', function () {
                    $("#tipsBox").hide()
                })

                // 表头周次合并
                const ths = $('div[lay-id=plateTime] .layui-table-header thead th');
                for (let i = 2; i < weeks; i++) {
                    if (i === 2) {
                        ths
                            .eq(i)
                            .attr('colspan', weeks)
                            .find('.layui-table-cell')
                            .text('周次')
                            .css({width: 61 * weeks - 1, 'text-align': 'center'})
                    } else {
                        ths.eq(i).addClass('layui-hide')
                    }
                }
                // 点击周次选择
                $('div[lay-id=plateTime] .layui-table-main td[data-field^="week"]').click(function () {
                    const rowData = getRowData('plateTime', plateTimeData);
                    if (checkTimeConflicts(rowData).length && !$(this).hasClass("cell-blue")) {
                        $("#dialogTip p").text("当前周次已安排上课，请勿重复安排");
                        return false;
                    }
                    $(this).toggleClass('cell-blue')
                })
                //   回显数据
                const trs = $('div[lay-id=plateTime] .layui-table-main tbody tr');
                trs.each(function (index, ele) {
                    // 周次回显
                    if (res.data[index].weeks && res.data[index].weeks.length > 0) {
                        const weeks = $(this).find('td[data-field^="week"]');
                        let weekSel = res.data[index].weeks.join(",");
                        weekSel = rangeStringToString(weekSel);
                        let numbers = weekSel.split(',');
                        weeks.each(function () {
                            const txt = $(this).text();
                            if (numbers.includes(txt)) {
                                $(this).addClass('cell-blue')
                            }
                        })
                    }
                    // 选项回显
                    const searchEld = $(this).find('.j-search-con');
                    searchEld.each(function (index1) {
                        const name = $(this).find('.schoolSel').attr('name');
                        const valData = res.data[index][name];
                        if (valData && valData.length > 0) {
                            echoSearch($(this), valData)
                        }
                    })
                })
            }
        })
    }

    function checkTimeConflicts(festivalsData) {
        const conflicts = [];
        const weekMap = {};
        festivalsData.forEach(festival => {
            const weekDays = festival.week.split(',');
            festival.weeks.forEach(weekNum => {
                if (!weekMap[weekNum]) weekMap[weekNum] = [];
                weekDays.forEach(day => {
                    weekMap[weekNum].push({
                        day: day.trim(),
                        start: parseInt(festival.startFestivals),
                        end: parseInt(festival.endFestivals)
                    });
                });
            });
        });
        Object.entries(weekMap).forEach(([weekNum, arrangements]) => {
            for (let i = 0; i < arrangements.length; i++) {
                for (let j = i + 1; j < arrangements.length; j++) {
                    const a = arrangements[i];
                    const b = arrangements[j];
                    if (a.day === b.day && !(a.end < b.start || b.end < a.start)) {
                        conflicts.push(`第${weekNum}周 ${a.day} ${a.start}-${a.end} 与 ${b.start}-${b.end} 冲突`);
                    }
                }
            }
        });
        return conflicts;
    }

    // 取消
    $('#plateTimeCancel,#addClassTimeDialog .close').click(function () {
        layer.close(layerTimeIndex)
    })

    //  sel回显
    function echoSearch(ele, val) {
        const valDate = val.split(',');
        ele.find('.schoolSel').val(val)
        ele.find('li').each(function () {
            const txt = $(this).text();
            if ($.inArray(txt, valDate) >= 0) {
                $(this).addClass('active')
            }
        })
    }

    $("#addClassTime").click(function () {
        if (selPlateTypeAry.length == 0) {
            return false;
        }
        let semester = [...new Set(selPlateTypeAry.map(item => item.semester))];
        if (semester.length > 1) {
            layer.msg('请选择同一学期的班级')
            return false;
        }
        setClassTime(semester)
    })

    //  添加
    let id = 1;
    $('#addPlateTime').click(function () {
        rowData.id = id++
        plateTimeData.push(JSON.parse(JSON.stringify(rowData)))
        getRowData('plateTime', plateTimeData)
        table.reload('plateTime', {
            data: plateTimeData
        })
    })

    //   操作
    table.on('tool(plateTime)', function (obj) {
        let i;
        let td;
        if (obj.event === 'del') {
            layer.confirm('真的删除该行吗？', function (index) {
                obj.del() // 删除对应行（tr）的DOM结构
                layer.close(index)
                const findDataIndex = plateTimeData.findIndex(function (i) {
                    return i.id === obj.data.id
                });
                plateTimeData.splice(findDataIndex, 1)
            })
        } else if (obj.event === 'oddWeek') {
            // 单周
            td = $(obj.tr).find('td[data-field^="week"]');
            td.removeClass('cell-blue')
            for (i = 1; i <= td.length; i++) {
                if (i % 2 === 1) {
                    td.eq(i - 1).addClass('cell-blue')
                }
            }
            plateTimeData[obj.index]['weekType'] = 1
        } else if (obj.event === 'evenWeek') {
            // 双周
            td = $(obj.tr).find('td[data-field^="week"]');
            td.removeClass('cell-blue')
            for (i = 1; i <= td.length; i++) {
                if (i % 2 === 0) {
                    td.eq(i - 1).addClass('cell-blue')
                }
            }
            plateTimeData[obj.index]['weekType'] = 2
        } else if (obj.event === 'allWeek') {
            // 全选
            td = $(obj.tr).find('td[data-field^="week"]');
            td.removeClass('cell-blue')
            for (i = 1; i <= td.length; i++) {
                td.eq(i - 1).addClass('cell-blue')
            }
            plateTimeData[obj.index]['weekType'] = 3
        } else if (obj.event === 'clearWeek') {
            // 清除选择
            td = $(obj.tr).find('td[data-field^="week"]');
            td.removeClass('cell-blue')
        }
    })

    //  确定
    $('#plateTimeSure').click(function () {
        const rowData = getRowData('plateTime', plateTimeData);
        if (checkTimeConflicts(rowData).length) {
            $("#dialogTip p").text("上课时间有冲突,请检查");
            dialogTipPop();
            return false;
        }
        let classTimeArr = [];
        rowData.forEach(item => {
            let classTime = {};
            let weeks = formatRange(item.weeks);
            let week = item.week;
            let startFestivals = "第" + item.startFestivals + "节";
            let endFestivals = "第" + item.endFestivals + "节";
            let festivals = startFestivals + "到" + endFestivals;
            classTime["weekNum"] = weeks;
            classTime["weekDay"] = dayOfWeekToNumber(week);
            classTime["curriNum"] = item.startFestivals + "-" + item.endFestivals;
            classTime["ctStr"] = weeks + "," + week + "," + festivals;
            classTimeArr.push(classTime);
        })
        let idArr = [];
        selPlateTypeAry.forEach(item => {
            idArr.push(item.id);
        })
        if (detailId) {
            idArr.push(detailId);
        }
        $.post(_VR_ + '/pc/cultivation/plateClass/updatePlateClassClassTime', {
            classTime: JSON.stringify(classTimeArr),
            idArr: idArr.join(",")
        }, function (result) {
            if (result.status) {
                layer.close(layerTimeIndex)
                table.reload('plateType')
                selPlateTypeAry = [];
            }
        });
    })

    function formatRange(arr) {
        if (!arr.length) return '';
        // 将字符串数组转换为整数数组，并去重和排序
        arr = [...new Set(arr.map(Number))].sort((a, b) => a - b);
        let result = [];
        for (let i = 0, start = arr[0]; i < arr.length; i++) {
            if (i + 1 === arr.length || arr[i] + 1 !== arr[i + 1]) {
                result.push(start === arr[i] ? `${start}` : `${start}-${arr[i]}`);
                start = arr[i + 1];
            }
        }
        return result.join(',');
    }

    function dayOfWeekToNumber(dayNamesStr) {
        const days = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
        const dayNames = dayNamesStr.split(",").map(name => name.trim());
        const numbers = dayNames.map(dayName => {
            const index = days.indexOf(dayName);
            if (index !== -1) {
                return index;
            } else if (dayName === "星期六") {
                return 6;
            } else if (dayName === "星期日") {
                return 0;
            }
            return null;
        });
        return numbers.filter(num => num !== null).join(",");
    }

    function numbersStringToDaysOfWeek(numbersStr) {
        const days = ["", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"];
        const numbers = numbersStr.split(",").map(num => parseInt(num.trim(), 10));
        const dayNames = numbers.map(number => {
            if (number >= 1 && number <= 7) {
                return days[number];
            }
            return null;
        });
        return dayNames.filter(name => name !== null).join(",");
    }

    // 跨页选择数据
    /*
     * @param {string} tableId 表格id
     * @param {Array} selData 选中数据集合
     * @param {number} showLen 显示长度
     * @param {number} totalCount 总数
     */
    function getSelData(tableId, selData, showLen, totalCount) {
        table.on('checkbox(' + tableId + ')', function (obj) {
            if (obj) {
                // 未选择全部数据，selData为选中的数据
                const checkedStatus = obj.checked;
                if (obj.type === 'one') {
                    if (checkedStatus) {
                        selData.push(obj.data)
                    } else {
                        const selIndex = selData.findIndex(item => item.id === obj.data.id);
                        if (selIndex > -1) {
                            selData.splice(selIndex, 1)
                        }
                    }
                } else if (obj.type === 'all') {
                    const checkStatus = table.checkStatus(tableId).data;
                    if (checkStatus.length === 0) {
                        selData = [];
                    } else {
                        checkStatus.forEach(element => {
                            const selIndex = selData.findIndex(item => element.id === item.id);
                            if (checkedStatus) {
                                if (selIndex < 0) {
                                    selData.push(element)
                                }
                            } else {
                                if (selIndex > -1) {
                                    selData.splice(selIndex, 1)
                                }
                            }
                        })
                    }
                }
            }
            selPlateTypeAry = selData;
            if (selData.length != 0) {
                $('#addClassTime').removeClass('layui-btn-disabled')
            } else {
                $('#addClassTime').addClass('layui-btn-disabled')
            }
        })
    }

    function rangeStringToString(rangeString) {
        if (rangeString === '') return '';
        const array = rangeString
            .split(',')
            .map(s => s.trim())
            .flatMap(range => {
                const parts = range.split('-').map(Number);
                const [start, end] = parts.length === 2 ? parts : [parts[0], parts[0]];
                return Array.from({length: end - start + 1}, (_, i) => start + i);
            });
        return array.join(',');
    }

    //   获取行数据
    function getRowData(id, data) {
        const trsData = [];
        const trs = $('div[lay-id=' + id + '] .layui-table-main tbody tr');
        trs.each(function (index, ele) {
            let trParams = {}
            let week = []
            $(this)
                .find('.cell-blue .layui-table-cell')
                .each(function () {
                    week.push($(this).text())
                })
            $(this)
                .find('.schoolSel')
                .each(function () {
                    const name = $(this).attr('name');
                    trParams[name] = $(this).val()
                })
            trParams['weeks'] = week
            $.extend(data[index], trParams)
            trsData.push(trParams)
        })
        return trsData
    }

    $('body').on('click', '#addPlanCorrespond .j-search-con .schoolSel', function (e) {
        let idx = $(this).parents(".layui-form-item").index();
        let selectHtml = "";
        let courseName = selPlateTypeAry[0].courseName.split(",");
        let courseId = selPlateTypeAry[0].courseId.split(",");
        let courseType = selPlateTypeAry[0].courseType;
        if (idx === 0) {
            selectHtml += "<li>" + selPlateTypeAry[0].semester + "</li>";
        }
        if (idx === 1) {
            selectHtml += "<li>" + selPlateTypeAry[0].plateTypeName + "</li>";
        }
        if (idx === 2) {
            selectHtml += "<li>" + selPlateTypeAry[0].plateTypeLevelName + "</li>";
        }
        if (idx === 3) {
            for (let i = 0; i < courseName.length; i++) {
                selectHtml += "<li id = \"" + courseId[i] + "\" courseType = \"" + courseType + "\">" + courseName[i] + "</li>";
            }
        }
        $(this).parent().find("ul").html(selectHtml);
    });

    $('body').on('click', '.j-search-con.multiple-box .j-select-year li ', function (e) {
        if (checkTimeConflicts(getRowData('plateTime', plateTimeData)).length) {
            $("#dialogTip p").text("当前星期已安排上课，请勿重复安排");
            dialogTipPop();
            return false;
        }
    });

    $('body').on('click', '.j-search-con.single-box .j-select-year li ', function (e) {
        if (checkTimeConflicts(getRowData('plateTime', plateTimeData)).length) {
            $("#dialogTip p").text("当前节次已安排上课，请勿重复安排");
            dialogTipPop();
            return false;
        }
    });

    let dialogTip;

    function dialogTipPop() {
        dialogTip = layer.open({
            type: 1,
            title: false,
            closeBtn: false,
            shadeClose: false,
            isOutAnim: true,
            content: $('#dialogTip'),
            area: ['auto', 'auto'],
            success: function () {
            }
        })
    }

    $("#tipSuccessBtn").click(function () {
        layer.close(dialogTip)
    })
})
